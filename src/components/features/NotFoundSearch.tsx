"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

interface NotFoundSearchProps {
  className?: string;
  translations: {
    searchPlaceholder: string;
    searchButton: string;
    searchAriaLabel: string;
  };
}

/**
 * Search component for the 404 page
 * Allows users to search for content when they land on a non-existent page
 */
const NotFoundSearch = ({ className = '', translations }: NotFoundSearchProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const router = useRouter();

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    
    try {
      // For now, we'll redirect to the homepage with a search parameter
      // In a real application, this would integrate with your search system
      const searchParams = new URLSearchParams();
      searchParams.set('q', searchQuery.trim());
      
      // Redirect to homepage with search query
      // You can modify this to redirect to a dedicated search page
      router.push(`/?${searchParams.toString()}`);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(e as any);
    }
  };

  return (
    <div className={`w-full max-w-md mx-auto ${className}`}>
      <form
        onSubmit={handleSearch}
        className="relative"
        role="search"
        aria-label={translations.searchAriaLabel}
      >
        <div className="relative">
          <Input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={translations.searchPlaceholder}
            className="search-input pr-12"
            disabled={isSearching}
            aria-label={translations.searchAriaLabel}
          />
          <Button
            type="submit"
            size="sm"
            disabled={isSearching || !searchQuery.trim()}
            className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
            aria-label={translations.searchButton}
          >
            <Search className="h-4 w-4" />
          </Button>
        </div>
      </form>
      
      {/* Search suggestions or popular links could go here */}
      <div className="mt-4 text-sm text-muted-foreground text-center">
        <p>Try searching for features, pricing, or help topics</p>
      </div>
    </div>
  );
};

export default NotFoundSearch;
