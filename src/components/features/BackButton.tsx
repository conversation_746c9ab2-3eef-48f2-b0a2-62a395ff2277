"use client";

import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface BackButtonProps {
  children: React.ReactNode;
  ariaLabel?: string;
  className?: string;
}

/**
 * Client-side back button component
 * Handles browser history navigation
 */
const BackButton = ({ children, ariaLabel, className }: BackButtonProps) => {
  const handleGoBack = () => {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      // Fallback to home page if no history
      window.location.href = '/';
    }
  };

  return (
    <Button 
      variant="outline" 
      size="lg" 
      onClick={handleGoBack}
      className={className}
      aria-label={ariaLabel}
    >
      <ArrowLeft className="w-4 h-4 mr-2" />
      {children}
    </Button>
  );
};

export default BackButton;
