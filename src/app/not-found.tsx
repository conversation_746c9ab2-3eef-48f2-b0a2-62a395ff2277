import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { ROUTES } from '@/constants/routes';
import type { Metadata } from 'next';

// Metadata for the 404 page
export const metadata: Metadata = {
  title: '404 - Page Not Found | FluxAI Studio',
  description: 'The page you\'re looking for could not be found. Return to FluxAI Studio homepage or search for what you need.',
  robots: 'noindex, nofollow',
};

/**
 * Professional 404 Not Found page
 * Clean, centered design with proper visual hierarchy
 */
export default function NotFound() {
  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-background via-background to-accent/5 flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8">
      <div className="w-full max-w-2xl mx-auto text-center">
        <div className="space-y-8 sm:space-y-10 lg:space-y-12">
          {/* Icon with enhanced styling */}
          <div className="flex justify-center">
            <div className="inline-flex items-center justify-center w-24 h-24 sm:w-28 sm:h-28 lg:w-32 lg:h-32 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg backdrop-blur-sm">
              <span className="text-3xl sm:text-4xl lg:text-5xl font-mono font-bold text-primary">&lt;/&gt;</span>
            </div>
          </div>

          {/* 404 Title with improved typography */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h1 className="text-7xl sm:text-8xl lg:text-9xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-foreground via-foreground to-foreground/80 tracking-tight">
                404
              </h1>
              <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground/90 tracking-tight">
                Page Not Found
              </h2>
            </div>
            <p className="text-lg sm:text-xl text-muted-foreground max-w-lg mx-auto leading-relaxed px-4">
              Sorry, the page you are looking for doesn't exist or has been moved.
              Let's get you back on track.
            </p>
          </div>

          {/* Enhanced button with better spacing */}
          <div className="pt-6 sm:pt-8">
            <Button
              asChild
              size="lg"
              className="px-8 py-4 sm:px-10 sm:py-5 text-base sm:text-lg font-semibold bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 focus:ring-4 focus:ring-primary/20"
              aria-label="Navigate back to homepage"
            >
              <Link href={ROUTES.HOME}>
                Back to Home
              </Link>
            </Button>
          </div>

          {/* Optional: Additional helpful text */}
          <div className="pt-4 sm:pt-6">
            <p className="text-sm text-muted-foreground/80">
              If you believe this is an error, please{' '}
              <Link
                href={ROUTES.CONTACT || '/contact'}
                className="text-primary hover:text-primary/80 underline underline-offset-4 transition-colors"
              >
                contact us
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
