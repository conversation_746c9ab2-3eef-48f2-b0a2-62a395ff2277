import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import BackButton from '@/components/features/BackButton';
import Link from 'next/link';
import { ROUTES } from '@/constants/routes';
import { Home, Search, FileQuestion } from 'lucide-react';
import type { Metadata } from 'next';

// Metadata for the 404 page
export const metadata: Metadata = {
  title: '404 - Page Not Found | FluxAI Studio',
  description: 'The page you\'re looking for could not be found. Return to FluxAI Studio homepage or search for what you need.',
  robots: 'noindex, nofollow',
};

/**
 * Localized 404 Not Found page
 * Provides helpful navigation options and search functionality
 */
export default function LocalizedNotFound() {
  // Static content for better reliability
  const suggestions = [
    "Check the URL for typos",
    "Go back to the previous page",
    "Visit our homepage",
    "Use the search function below"
  ];
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-accent/5 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl mx-auto">
        <div className="text-center space-y-8">
          {/* 404 Illustration */}
          <div className="relative">
            <div className="inline-flex items-center justify-center w-32 h-32 mx-auto mb-8 rounded-full bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
              <FileQuestion
                className="w-16 h-16 text-primary"
                aria-label="404 error illustration"
              />
            </div>

            {/* Floating elements for visual appeal */}
            <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary/20 rounded-full animate-pulse" />
            <div className="absolute -bottom-2 -right-6 w-6 h-6 bg-accent/30 rounded-full animate-pulse delay-300" />
          </div>

          {/* Main heading */}
          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl font-bold theme-gradient-text">
              404 - Oops! Page Not Found
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              The page you're looking for doesn't exist or has been moved. Don't worry, it happens to the best of us!
            </p>
          </div>

          {/* Suggestions */}
          <Card className="theme-card max-w-2xl mx-auto">
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold mb-4 theme-text-primary">
                Here's what you can do:
              </h2>
              <ul className="space-y-2 text-left">
                {suggestions.map((item: string, index: number) => (
                  <li key={index} className="flex items-center gap-3 text-muted-foreground">
                    <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                    {item}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Simple search form */}
          <div className="space-y-4">
            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <Search className="w-4 h-4" />
              <span className="text-sm font-medium">Search for what you need</span>
            </div>
            <div className="max-w-md mx-auto">
              <form className="relative" role="search" aria-label="Search for content on our website">
                <input
                  type="text"
                  placeholder="Search for content..."
                  className="search-input pr-12"
                  aria-label="Search for content on our website"
                />
                <Button
                  type="submit"
                  size="sm"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                  aria-label="Search"
                >
                  <Search className="h-4 w-4" />
                </Button>
              </form>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              asChild
              size="lg"
              className="min-w-[160px]"
              aria-label="Navigate to homepage"
            >
              <Link href={ROUTES.HOME}>
                <Home className="w-4 h-4 mr-2" />
                Go to Homepage
              </Link>
            </Button>

            <BackButton
              className="min-w-[160px]"
              ariaLabel="Go back to previous page"
            >
              Go Back
            </BackButton>
          </div>

          {/* Quick navigation links */}
          <Card className="theme-card max-w-2xl mx-auto">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4 theme-text-primary">
                Quick Links
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                <Link
                  href={ROUTES.HOME}
                  className="nav-link text-center p-3 rounded-lg hover:bg-accent/50 transition-colors"
                >
                  Home
                </Link>
                <Link
                  href={ROUTES.FEATURES}
                  className="nav-link text-center p-3 rounded-lg hover:bg-accent/50 transition-colors"
                >
                  Features
                </Link>
                <Link
                  href={ROUTES.PRICING}
                  className="nav-link text-center p-3 rounded-lg hover:bg-accent/50 transition-colors"
                >
                  Pricing
                </Link>
                <Link
                  href={ROUTES.ABOUT}
                  className="nav-link text-center p-3 rounded-lg hover:bg-accent/50 transition-colors"
                >
                  About
                </Link>
                <Link
                  href={ROUTES.CONTACT}
                  className="nav-link text-center p-3 rounded-lg hover:bg-accent/50 transition-colors"
                >
                  Contact
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
