import { getTranslations } from 'next-intl/server';
import { unstable_setRequestLocale } from 'next-intl/server';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import NotFoundSearch from '@/components/features/NotFoundSearch';
import BackButton from '@/components/features/BackButton';
import { Link } from '@/i18n/routing';
import { ROUTES } from '@/constants/routes';
import { Home, Search, FileQuestion } from 'lucide-react';
import type { Metadata } from 'next';

interface NotFoundProps {
  params: Promise<{ locale: string }>;
}

// Metadata for the 404 page
export async function generateMetadata({ params }: NotFoundProps): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations('notFound.meta');
  
  return {
    title: t('title'),
    description: t('description'),
    robots: 'noindex, nofollow',
  };
}

/**
 * Localized 404 Not Found page
 * Provides helpful navigation options and search functionality with internationalization
 */
export default async function LocalizedNotFound({ params }: NotFoundProps) {
  const { locale } = await params;
  unstable_setRequestLocale(locale);
  
  const t = await getTranslations('notFound');
  const navT = await getTranslations('notFound.navigation');
  const actionsT = await getTranslations('notFound.actions');
  const accessibilityT = await getTranslations('notFound.accessibility');

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-accent/5 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl mx-auto">
        <div className="text-center space-y-8">
          {/* 404 Illustration */}
          <div className="relative">
            <div className="inline-flex items-center justify-center w-32 h-32 mx-auto mb-8 rounded-full bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
              <FileQuestion 
                className="w-16 h-16 text-primary" 
                aria-label={accessibilityT('illustration')}
              />
            </div>
            
            {/* Floating elements for visual appeal */}
            <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary/20 rounded-full animate-pulse" />
            <div className="absolute -bottom-2 -right-6 w-6 h-6 bg-accent/30 rounded-full animate-pulse delay-300" />
          </div>

          {/* Main heading */}
          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl font-bold theme-gradient-text">
              {t('heading')}
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              {t('description')}
            </p>
          </div>

          {/* Suggestions */}
          <Card className="theme-card max-w-2xl mx-auto">
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold mb-4 theme-text-primary">
                {t('suggestions.title')}
              </h2>
              <ul className="space-y-2 text-left">
                {t.raw('suggestions.items').map((item: string, index: number) => (
                  <li key={index} className="flex items-center gap-3 text-muted-foreground">
                    <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                    {item}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Search functionality */}
          <div className="space-y-4">
            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <Search className="w-4 h-4" />
              <span className="text-sm font-medium">
                {locale === 'zh' ? '搜索您需要的内容' : 'Search for what you need'}
              </span>
            </div>
            <NotFoundSearch
              className="max-w-md mx-auto"
              translations={{
                searchPlaceholder: actionsT('searchPlaceholder'),
                searchButton: actionsT('searchButton'),
                searchAriaLabel: actionsT('searchAriaLabel'),
              }}
            />
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              asChild 
              size="lg" 
              className="min-w-[160px]"
              aria-label={accessibilityT('homeButton')}
            >
              <Link href={ROUTES.HOME}>
                <Home className="w-4 h-4 mr-2" />
                {actionsT('goHome')}
              </Link>
            </Button>
            
            <BackButton
              className="min-w-[160px]"
              ariaLabel={accessibilityT('backButton')}
            >
              {actionsT('goBack')}
            </BackButton>
          </div>

          {/* Quick navigation links */}
          <Card className="theme-card max-w-2xl mx-auto">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4 theme-text-primary">
                {navT('title')}
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                <Link 
                  href={ROUTES.HOME}
                  className="nav-link text-center p-3 rounded-lg hover:bg-accent/50 transition-colors"
                >
                  {navT('links.home')}
                </Link>
                <Link 
                  href={ROUTES.FEATURES}
                  className="nav-link text-center p-3 rounded-lg hover:bg-accent/50 transition-colors"
                >
                  {navT('links.features')}
                </Link>
                <Link 
                  href={ROUTES.PRICING}
                  className="nav-link text-center p-3 rounded-lg hover:bg-accent/50 transition-colors"
                >
                  {navT('links.pricing')}
                </Link>
                <Link 
                  href={ROUTES.ABOUT}
                  className="nav-link text-center p-3 rounded-lg hover:bg-accent/50 transition-colors"
                >
                  {navT('links.about')}
                </Link>
                <Link 
                  href={ROUTES.CONTACT}
                  className="nav-link text-center p-3 rounded-lg hover:bg-accent/50 transition-colors"
                >
                  {navT('links.contact')}
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
