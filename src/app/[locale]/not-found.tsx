import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ROUTES } from '@/constants/routes';
import type { Metadata } from 'next';

// Metadata for the 404 page
export const metadata: Metadata = {
  title: '404 - Page Not Found | FluxAI Studio',
  description: 'The page you\'re looking for could not be found. Return to FluxAI Studio homepage or search for what you need.',
  robots: 'noindex, nofollow',
};

/**
 * Simple localized 404 Not Found page
 * Clean and minimal design
 */
export default function LocalizedNotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="text-center space-y-8">
        {/* Simple Icon */}
        <div className="inline-flex items-center justify-center w-20 h-20 mx-auto rounded-xl bg-foreground text-background">
          <span className="text-2xl font-mono font-bold">&lt;/&gt;</span>
        </div>

        {/* 404 Title */}
        <div className="space-y-4">
          <h1 className="text-6xl md:text-8xl font-bold text-foreground">
            404
          </h1>
          <p className="text-lg text-muted-foreground max-w-md mx-auto">
            Sorry, the page you are looking for does not exist.
          </p>
        </div>

        {/* Back to Home Button */}
        <div className="pt-4">
          <Button
            asChild
            size="lg"
            className="px-8 py-3 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg font-medium transition-colors"
            aria-label="Navigate to homepage"
          >
            <Link href={ROUTES.HOME}>
              Back to home
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
